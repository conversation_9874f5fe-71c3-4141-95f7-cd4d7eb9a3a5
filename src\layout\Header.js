import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
const Header = () => {
  const [showCatalogDropdown, setShowCatalogDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCatalogDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const navStyle = {
    display: "flex",
    listStyle: "none",
    margin: 0,
    padding: 0,
    gap: "30px",
  };

  const iconStyle = {
    width: "20px",
    height: "20px",
    cursor: "pointer",
    transition: "transform 0.3s ease",
  };

  const dropdownMenuStyle = {
    position: "fixed",
    top: "64px",
    left: "0",
    right: "0",
    backgroundColor: "#ffffff",
    width: "100%",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    zIndex: 99,
    padding: "40px 150px",
    display: showCatalogDropdown ? "block" : "none",
  };

  const dropdownContentStyle = {
    display: "flex",
    margin: "0 auto",
    gap: "60px",
  };

  const categoryColumnStyle = {
    width: "250px",
    paddingTop: "12px",
  };

  const categoryTitleStyle = {
    fontSize: "16px",
    fontWeight: "500",
    color: "#333",
    marginBottom: "0",
    textTransform: "none",
    letterSpacing: "0",
    cursor: "pointer",
    padding: "16px 20px",
    borderBottom: "1px solid #e5e5e5",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    transition: "background-color 0.2s ease",
    backgroundColor: "#fff",
  };

  const arrowStyle = {
    fontSize: "18px",
    color: "#666",
    fontWeight: "normal",
  };

  return (
    <>
      <header
        style={{
          backgroundColor: "#CDFF9A",
          padding: "15px 50px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          position: "relative",
          zIndex: 100,
        }}
      >
        <button
          onClick={() => navigate("/")}
          style={{
            fontSize: "24px",
            fontWeight: "bold",
            color: "#000000",
            textDecoration: "none",
            display: "flex",
            alignItems: "center",
            background: "none",
            border: "none",
            cursor: "pointer",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              lineHeight: "1",
            }}
          >
            <span>HA</span>
            <span>SPORT</span>
          </div>
        </button>

        <nav>
          <ul style={navStyle}>
            <li>
              <button
                onClick={() => navigate("/")}
                style={{
                  textDecoration: "none",
                  color: "#000000",
                  fontSize: "14px",
                  fontWeight: "bold",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  transition: "color 0.3s ease",
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#ffffff")}
                onMouseLeave={(e) => (e.target.style.color = "#000000")}
              >
                HOME
              </button>
            </li>
            <li>
              <div
                style={{
                  textDecoration: "none",
                  color: "#000000",
                  fontSize: "14px",
                  fontWeight: "bold",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  transition: "color 0.3s ease",
                  cursor: "pointer",
                  userSelect: "none",
                }}
                onClick={() => setShowCatalogDropdown(!showCatalogDropdown)}
                onMouseEnter={(e) => (e.target.style.color = "#ffffff")}
                onMouseLeave={(e) => (e.target.style.color = "#000000")}
              >
                CATALOGS
              </div>
            </li>
            <li>
              <button
                onClick={() => navigate("/blog")}
                style={{
                  textDecoration: "none",
                  color: "#000000",
                  fontSize: "14px",
                  fontWeight: "bold",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  transition: "color 0.3s ease",
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#ffffff")}
                onMouseLeave={(e) => (e.target.style.color = "#000000")}
              >
                BLOG
              </button>
            </li>
            <li>
              <button
                onClick={() => navigate("/shop")}
                style={{
                  textDecoration: "none",
                  color: "#000000",
                  fontSize: "14px",
                  fontWeight: "bold",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  transition: "color 0.3s ease",
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#ffffff")}
                onMouseLeave={(e) => (e.target.style.color = "#000000")}
              >
                SHOP
              </button>
            </li>
            <li>
              <button
                onClick={() => navigate("/contact")}
                style={{
                  textDecoration: "none",
                  color: "#000000",
                  fontSize: "14px",
                  fontWeight: "bold",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                  transition: "color 0.3s ease",
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#ffffff")}
                onMouseLeave={(e) => (e.target.style.color = "#000000")}
              >
                CONTACT US
              </button>
            </li>
          </ul>
        </nav>

        <div
          style={{
            display: "flex",
            gap: "15px",
            alignItems: "center",
          }}
        >
          <svg
            style={iconStyle}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>

          <svg
            style={iconStyle}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>

          <svg
            style={iconStyle}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>

          <svg
            style={iconStyle}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          </svg>
        </div>
      </header>

      <div style={dropdownMenuStyle} ref={dropdownRef}>
        <div style={dropdownContentStyle}>
          <div style={categoryColumnStyle}>
            <div
              style={categoryTitleStyle}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = "#f8f9fa";
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = "#fff";
              }}
              onClick={() => navigate("/catalog/activity")}
            >
              <span>Activity</span>
              <span style={arrowStyle}>›</span>
            </div>
            <div
              style={categoryTitleStyle}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = "#f8f9fa";
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = "#fff";
              }}
              onClick={() => navigate("/catalog/equipment")}
            >
              <span>Equipment</span>
              <span style={arrowStyle}>›</span>
            </div>
            <div
              style={categoryTitleStyle}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = "#f8f9fa";
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = "#fff";
              }}
              onClick={() => navigate("/catalog/womens")}
            >
              <span>Women's</span>
              <span style={arrowStyle}>›</span>
            </div>
            <div
              style={{
                ...categoryTitleStyle,
                borderBottom: "none"
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = "#f8f9fa";
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = "#fff";
              }}
              onClick={() => navigate("/catalog/mens")}
            >
              <span>Men's</span>
              <span style={arrowStyle}>›</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;
